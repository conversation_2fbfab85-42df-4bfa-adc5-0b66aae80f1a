/**
 * Question Prompts Component Styles
 *
 * 极简设计的问题提示组件
 * 专注于功能性和可读性，去除所有装饰性元素
 */

/* 主容器 - 完全匹配chat input的布局 */

.question-prompts-main-title {
    font-size: 1.5rem; /* Adjusted title size */
    font-weight: 600;
    color: var(--color-text-primary);
    line-height: 1.3;
    margin-bottom: 0.75rem; /* Space before subtitle */
    text-align: left; /* Match subtitle alignment */
}

.question-prompts-container {
    width: 100%;
    margin: 0 auto;
}



.question-prompts-subtitle {
    font-size: 0.875rem;
    font-weight: 400;
    color: var(--color-text-secondary);
    line-height: 1.4;
    margin-bottom: 1rem;
    text-align: left; /* 左对齐 */
}

/* 问题列表区域 - 带渐变效果的布局 */
.question-prompts-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-height: 31rem; /* Further increased height for desktop */
    overflow-y: auto; /* Ensure scrollability */
    position: relative;
    /* 移除padding，让按钮占满宽度 */
}



/* 问题项 - 极简设计 */
.question-prompt-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem; /* 适中的padding */
    background: transparent; /* 透明背景 */
    border: 1px solid var(--color-border-primary);
    border-radius: 0.5rem; /* 简单圆角 */
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    width: 100%;
    min-height: 2.5rem; /* 适中的高度 */
    opacity: 0;
    animation: questionItemFadeIn 0.4s ease forwards;
}

.question-prompt-item:hover {
    background: var(--color-bg-secondary);
    border-color: var(--color-border-secondary);
}

.question-prompt-item:active {
    background: var(--color-bg-tertiary);
}

/* 问题文本 - 简洁排版 */
.question-prompt-text {
    font-size: 0.875rem;
    font-weight: 400;
    color: var(--color-text-primary);
    line-height: 1.4;
    word-break: break-word;
    flex: 1;
}

/* Vue过渡动画 - 问题列表的进入和退出，带延迟 */
.question-list-enter-active {
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    transition-delay: 0.4s; /* 等待标题动画完成 */
}

.question-list-leave-active {
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.question-list-enter-from {
    opacity: 0;
    transform: translateY(15px);
}

.question-list-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

/* 即时隐藏过渡动画 - 用于切换到对话历史时 */
.question-list-instant-enter-active {
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    transition-delay: 0.4s; /* 等待标题动画完成 */
}

.question-list-instant-leave-active {
    transition: none; /* 立即消失，无动画 */
}

.question-list-instant-enter-from {
    opacity: 0;
    transform: translateY(15px);
}

.question-list-instant-leave-to {
    opacity: 0;
    /* 无需transform，因为没有过渡动画 */
}

/* 暗色模式适配 - 简洁设计 */
[data-theme="dark"] .question-prompt-item {
    border-color: var(--color-border-primary);
}

[data-theme="dark"] .question-prompt-item:hover {
    background: var(--color-bg-secondary);
    border-color: var(--color-border-secondary);
}

/* 简洁动画系统 */
@keyframes questionItemFadeIn {
    0% {
        opacity: 0;
        transform: translateY(8px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 - 简洁适配 */
@media (max-width: 768px) {
    .question-prompts-main-title {
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
    }
    .question-prompts-subtitle {
        font-size: 0.8rem;
    }

    .question-prompt-item {
        padding: 0.625rem 0.875rem;
        min-height: 2.25rem;
    }

    .question-prompt-text {
        font-size: 0.8rem;
    }

    .question-prompts-list {
        max-height: 22rem; /* Further increased height for tablet */
        gap: 0.375rem;
    }
}

@media (max-width: 640px) {
    .question-prompts-main-title {
        font-size: 1.1rem;
    }
    .question-prompt-item {
        padding: 0.5rem 0.75rem;
        min-height: 2rem;
    }

    .question-prompt-text {
        font-size: 0.75rem;
    }

    .question-prompts-list {
        max-height: 20rem; /* Further increased height for mobile */
    }
}
