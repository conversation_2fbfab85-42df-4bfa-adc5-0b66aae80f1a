/**
 * Conversation Service
 *
 * Provides API services for conversation data
 * Centralizes all API calls for better separation of concerns
 */

/**
 * Fetch conversations with filters
 * @param {Object} options - Options for fetching conversations
 * @param {string} options.username - Filter by username
 * @param {string} options.conversation_id - Filter by conversation ID
 * @param {string} options.content_search - Search in message content
 * @param {string} options.bad_case_filter - Filter by bad case status ('only_bad', 'only_good', 'all')
 * @param {number} options.repair_status_filter - Filter by repair status (0, 1, 2)
 * @param {string} options.agent - Filter by agent name
 * @param {boolean} options.filter_admin - Whether to filter out admin users
 * @param {string} options.start_date - Filter by start date (YYYY-MM-DD)
 * @param {string} options.end_date - Filter by end date (YYYY-MM-DD)
 * @param {number} options.limit - Maximum number of conversations to return
 * @param {number} options.offset - Number of conversations to skip
 * @returns {Promise<Object>} Conversations data with total count
 */
export const fetchConversations = async ({
    username = '',
    conversation_id = '',
    content_search = '',
    bad_case_filter = 'all',
    repair_status_filter = null,
    agent = '',
    filter_admin = false,
    start_date = '',
    end_date = '',
    limit = 10,
    offset = 0
}) => {
    try {
        // Build query parameters
        const params = new URLSearchParams();

        if (username) params.append('username', username);
        if (conversation_id) params.append('conversation_id', conversation_id);
        if (content_search) params.append('content_search', content_search);
        if (bad_case_filter !== 'all') params.append('bad_case_filter', bad_case_filter);
        if (repair_status_filter !== null && repair_status_filter !== undefined) params.append('repair_status_filter', repair_status_filter);
        if (agent) params.append('agent', agent);
        if (filter_admin) params.append('filter_admin', 'true');
        if (start_date) params.append('start_date', start_date);
        if (end_date) params.append('end_date', end_date);

        params.append('limit', limit);
        params.append('offset', offset);

        // Make API request
        const response = await fetch(`/api/dashboard/logs?${params.toString()}`);

        if (!response.ok) {
            throw new Error(`Failed to fetch conversations: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        // Format the response data
        return {
            conversations: data.conversations || {},
            totalCount: data.total_count || 0
        };
    } catch (error) {
        console.error('Error fetching conversations:', error);
        throw error;
    }
};

/**
 * Fetch a single conversation by ID
 * @param {string} conversation_id - Conversation ID
 * @returns {Promise<Object|null>} Conversation data or null if not found
 */
export const fetchConversationById = async (conversation_id) => {
    try {
        if (!conversation_id) {
            throw new Error('Missing required parameter: conversation_id');
        }

        // Use the existing fetchConversations function with conversation_id filter
        const result = await fetchConversations({
            conversation_id: conversation_id,
            limit: 1,
            offset: 0
        });

        // Extract the conversation from the result
        const conversations = result.conversations || {};
        const conversationKeys = Object.keys(conversations);

        if (conversationKeys.length > 0) {
            const conversation = conversations[conversationKeys[0]];
            // Format the conversation data similar to formattedConversations in ConversationListLayout
            return {
                conversation_id: conversation.conversation_id,
                id: conversation.conversation_id,
                title: conversation.title || '无标题对话',
                username: conversation.username || '未知用户',
                email: conversation.email || '',
                is_bad_case: conversation.is_bad_case,
                agents: conversation.agents || [],
                messages: conversation.messages || [],
                lastMessageTime: new Date(conversation.last_message_time).toLocaleString(),
                timestamp: conversation.last_message_time,
                isBadCase: conversation.is_bad_case,
                messageCount: conversation.messages ? conversation.messages.length : 0,
            };
        }

        return null;
    } catch (error) {
        console.error('Error fetching conversation by ID:', error);
        throw error;
    }
};

/**
 * Mark a conversation as a bad case
 * @param {Object} options - Options for marking a bad case
 * @param {string} options.conversation_id - Conversation ID
 * @param {boolean} options.is_bad_case - Whether the conversation is a bad case
 * @param {string} options.username - Username
 * @param {string} options.email - Email
 * @returns {Promise<Object>} Response data
 */
export const markBadCase = async ({ conversation_id, is_bad_case = true, username, email }) => {
    try {
        if (!conversation_id || !username) {
            throw new Error('Missing required parameters: conversation_id and username are required');
        }

        const response = await fetch('/api/dashboard/mark_bad_case', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                conversation_id,
                is_bad_case,
                username,
                email
            })
        });

        if (!response.ok) {
            throw new Error(`Failed to mark bad case: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error marking bad case:', error);
        throw error;
    }
};

/**
 * Get the bad case status of a conversation
 * @param {string} conversation_id - Conversation ID
 * @param {string} username - Username
 * @param {string} email - Email
 * @returns {Promise<boolean>} Whether the conversation is a bad case
 */
export const getBadCaseStatus = async (conversation_id, username, email) => {
    try {
        if (!conversation_id || !username) {
            throw new Error('Missing required parameters: conversation_id and username are required');
        }

        const params = new URLSearchParams();
        params.append('username', username);
        if (email) params.append('email', email);

        const response = await fetch(`/api/dashboard/bad_case_status/${conversation_id}?${params.toString()}`);

        if (!response.ok) {
            throw new Error(`Failed to get bad case status: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data.is_bad_case || false;
    } catch (error) {
        console.error('Error getting bad case status:', error);
        return false;
    }
};

/**
 * Share a conversation
 * @param {string} conversation_id - Conversation ID
 * @returns {Promise<Object>} Share data with share_id and share_url
 */
export const shareConversation = async (conversation_id) => {
    try {
        if (!conversation_id) {
            throw new Error('Missing required parameter: conversation_id');
        }

        const response = await fetch('/api/share_conversation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                conversation_id
            })
        });

        if (!response.ok) {
            throw new Error(`Failed to share conversation: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error sharing conversation:', error);
        throw error;
    }
};

/**
 * Update repair status of a bad case conversation
 * @param {string} conversation_id - Conversation ID
 * @param {number} repair_status - Repair status (0=未修复, 1=已修复, 2=暂不修复)
 * @param {string} username - Username (optional)
 * @param {string} email - Email (optional)
 * @returns {Promise<Object>} Response with status and repair_status
 */
export const updateRepairStatus = async (conversation_id, repair_status, username = null, email = null) => {
    try {
        if (!conversation_id) {
            throw new Error('Missing required parameter: conversation_id');
        }

        if (![0, 1, 2].includes(repair_status)) {
            throw new Error('Invalid repair_status. Must be 0, 1, or 2');
        }

        const requestBody = {
            conversation_id,
            repair_status
        };

        if (username) requestBody.username = username;
        if (email) requestBody.email = email;

        const response = await fetch('/api/dashboard/update_repair_status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`Failed to update repair status: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    } catch (error) {
        console.error('Error updating repair status:', error);
        throw error;
    }
};
